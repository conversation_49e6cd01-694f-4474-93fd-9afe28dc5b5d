const path = require('path');
const fs = require('fs');

// 加载环境变量
require('dotenv').config({ path: path.join(__dirname, '.env') });

/**
 * Love项目配置管理模块
 * 统一管理所有配置项，支持环境变量覆盖
 */

// 配置验证函数
function validateConfig() {
    const errors = [];

    // 验证端口号
    const port = parseInt(process.env.PORT);
    if (isNaN(port) || port < 1 || port > 65535) {
        errors.push('PORT must be a valid port number (1-65535)');
    }

    // 验证域名
    if (!process.env.BASE_DOMAIN) {
        errors.push('BASE_DOMAIN is required');
    }

    // 验证数据库路径
    if (!process.env.DB_PATH) {
        errors.push('DB_PATH is required');
    }

    // 验证Cloudinary配置 (如果启用)
    if (process.env.CLOUDINARY_ENABLED === 'true') {
        const requiredSecrets = [
            'CLOUDINARY_SECRET_YU0',
            'CLOUDINARY_SECRET_YU1',
            'CLOUDINARY_SECRET_YU2',
            'CLOUDINARY_SECRET_YU3',
            'CLOUDINARY_SECRET_YU4',
            'CLOUDINARY_SECRET_YU5'
        ];

        requiredSecrets.forEach(secret => {
            if (!process.env[secret]) {
                errors.push(`${secret} is required when Cloudinary is enabled`);
            }
        });
    }

    // 验证三层视频架构配置 (如果启用)
    if (process.env.VIDEO_DELIVERY_ENABLED === 'true') {
        // 验证R2配置
        if (process.env.CLOUDFLARE_R2_ENABLED === 'true') {
            const requiredR2Vars = [
                'CLOUDFLARE_ACCOUNT_ID',
                'CLOUDFLARE_R2_ACCESS_KEY_ID',
                'CLOUDFLARE_R2_SECRET_ACCESS_KEY',
                'CLOUDFLARE_R2_ENDPOINT',
                'CLOUDFLARE_R2_BUCKET'
            ];

            requiredR2Vars.forEach(varName => {
                if (!process.env[varName]) {
                    errors.push(`${varName} is required when R2 is enabled`);
                }
            });
        }

        // 验证VPS配置
        if (process.env.VPS_ENABLED === 'true') {
            if (!process.env.VPS_BASE_URL) {
                errors.push('VPS_BASE_URL is required when VPS is enabled');
            }
        }
    }

    if (errors.length > 0) {
        console.error('Configuration validation errors:');
        errors.forEach(error => console.error(`  - ${error}`));
        throw new Error('Invalid configuration');
    }
}

// 主配置对象
const config = {
    // 服务器配置 - 直接使用.env配置
    server: {
        port: parseInt(process.env.PORT) || 1314,
        env: process.env.NODE_ENV || 'production',
        corsOrigin: process.env.CORS_ORIGIN || '*',
        staticFilesCache: parseInt(process.env.STATIC_FILES_CACHE) || 3600
    },

    // 域名配置 - 统一环境变量管理（v2.1 修复）
    domain: {
        // 基础域名配置（从环境变量获取）
        base: process.env.BASE_DOMAIN || 'love.yuh.cool',
        url: process.env.BASE_URL || 'https://love.yuh.cool',

        // API相关URL配置（动态生成）
        get api() {
            const baseUrl = process.env.BASE_URL || 'https://love.yuh.cool';
            return {
                base: `${baseUrl}/api`,
                messages: `${baseUrl}/api/messages`,
                timeline: `${baseUrl}/api/timeline`,
                memories: `${baseUrl}/api/memories`,
                modernQuotes: `${baseUrl}/api/modern-quotes`,
                health: `${baseUrl}/api/health`,
                config: `${baseUrl}/api/config`
            };
        },

        // 页面路径配置（动态生成）
        get pages() {
            const baseUrl = process.env.BASE_URL || 'https://love.yuh.cool';
            return {
                home: `${baseUrl}/`,
                togetherDays: `${baseUrl}/together-days`,
                anniversary: `${baseUrl}/anniversary`,
                meetings: `${baseUrl}/meetings`,
                memorial: `${baseUrl}/memorial`,
                verify: `${baseUrl}/verify`
            };
        },

        // 静态资源路径配置（动态生成）
        get assets() {
            const baseUrl = process.env.BASE_URL || 'https://love.yuh.cool';
            return {
                base: `${baseUrl}/src/client`,
                styles: `${baseUrl}/src/client/styles`,
                scripts: `${baseUrl}/src/client/scripts`,
                fonts: `${baseUrl}/src/client/assets/fonts`,
                images: `${baseUrl}/src/client/assets/images`,
                videos: `${baseUrl}/src/client/assets/videos`
            };
        },

        // 兼容路径配置（动态生成）
        get legacy() {
            const baseUrl = process.env.BASE_URL || 'https://love.yuh.cool';
            return {
                background: `${baseUrl}/background`,
                fonts: `${baseUrl}/fonts`,
                test: `${baseUrl}/test`
            };
        },

        // 环境变量直接访问（保持兼容性）
        env: {
            base: process.env.BASE_DOMAIN || 'love.yuh.cool',
            url: process.env.BASE_URL || 'https://love.yuh.cool'
        }
    },
    
    // 数据库配置 - 直接使用.env配置
    database: {
        path: process.env.DB_PATH || './data/love_messages.db',
        backupDir: process.env.DB_BACKUP_DIR || './data/backups'
    },

    // 日志配置 - 直接使用.env配置
    logging: {
        level: process.env.LOG_LEVEL || 'info',
        file: process.env.LOG_FILE || './logs/backend.log'
    },
    
    // 特殊日期配置 - 直接使用.env配置
    dates: {
        yuBirthday: process.env.YU_BIRTHDAY || '01-16',
        wangBirthday: process.env.WANG_BIRTHDAY || '04-15',
        loveStartDate: process.env.LOVE_START_DATE || '2023-01-01'
    },

    // API配置 - 直接使用.env配置
    api: {
            prefix: process.env.API_PREFIX || '/api',
        messagesEndpoint: process.env.MESSAGES_ENDPOINT || '/messages',
        healthEndpoint: process.env.HEALTH_ENDPOINT || '/health'
    },

    // Cloudinary多账户配置 (方案A - 高画质)
    cloudinary: {
        // 全局开关
        enabled: process.env.CLOUDINARY_ENABLED === 'true' || true,

        // 方案A压缩参数 (固定配置，不可修改)
        compression: {
            preset: 'extreme_quality',  // 方案A标识
            crf: 18,                    // 视觉无损质量
            speed: 'veryslow',          // 最佳压缩效率
            resolution: '2560:1440',    // 2K分辨率
            audioBitrate: '256k',       // 高质量音频
            pixelFormat: 'yuv420p',     // 兼容性像素格式
            profile: 'high',            // H.264高级配置
            level: '4.1'                // 兼容性级别
        },

        // 多账户配置 (严格按照现有架构)
        accounts: {
            INDEX: {
                cloudName: 'dcglebc2w',
                apiKey: '***************',
                apiSecret: process.env.CLOUDINARY_SECRET_YU0,
                folder: 'love-website',
                quota: 25 * 1024 * 1024 * 1024, // 25GB
                priority: 1
            },
            ANNIVERSARY: {
                cloudName: 'drhqbbqxz',
                apiKey: '***************',
                apiSecret: process.env.CLOUDINARY_SECRET_YU1,
                folder: 'love-website',
                quota: 25 * 1024 * 1024 * 1024,
                priority: 1
            },
            MEETINGS: {
                cloudName: 'dkqnm9nwr',
                apiKey: '***************',
                apiSecret: process.env.CLOUDINARY_SECRET_YU2,
                folder: 'love-website',
                quota: 25 * 1024 * 1024 * 1024,
                priority: 1
            },
            MEMORIAL: {
                cloudName: 'ds14sv2gh',
                apiKey: '822751152715929',
                apiSecret: process.env.CLOUDINARY_SECRET_YU3,
                folder: 'love-website',
                quota: 25 * 1024 * 1024 * 1024,
                priority: 1
            },
            TOGETHER_DAYS: {
                cloudName: 'dpq95x5nf',
                apiKey: '934251748658618',
                apiSecret: process.env.CLOUDINARY_SECRET_YU4,
                folder: 'love-website',
                quota: 25 * 1024 * 1024 * 1024,
                priority: 1
            },
            BACKUP: {
                cloudName: 'dtsgvqrna',
                apiKey: '567337797774118',
                apiSecret: process.env.CLOUDINARY_SECRET_YU5,
                folder: 'love-website',
                quota: 25 * 1024 * 1024 * 1024,
                priority: 2
            }
        },

        // 页面映射 (固定映射关系)
        pageMapping: {
            'home': 'INDEX',
            'anniversary': 'ANNIVERSARY',
            'meetings': 'MEETINGS',
            'memorial': 'MEMORIAL',
            'together-days': 'TOGETHER_DAYS'
        },

        // 加载器配置
        loader: {
            timeout: 15000,             // 15秒超时
            retries: 2,                 // 重试2次
            localFallback: true,        // 启用本地回退
            localPath: '/src/client/assets/videos-compressed',
            quality: 'q_auto:best,f_auto'  // Cloudinary高画质参数
        },

        // 清理配置
        cleanup: {
            enabled: true,              // 启用清理
            beforeUpload: true,         // 上传前清理
            mode: 'complete'            // 完全清理模式
        }
    },

    // 三层视频架构配置 (v2.2 新增)
    videoDelivery: {
        enabled: process.env.VIDEO_DELIVERY_ENABLED === 'true',

        layers: {
            primary: {
                type: 'cloudflare_r2',
                enabled: process.env.CLOUDFLARE_R2_ENABLED === 'true',
                timeout: parseInt(process.env.R2_TIMEOUT) || 6000,
                videoQuality: 'original'
            },
            secondary: {
                type: 'cloudinary',
                enabled: process.env.CLOUDINARY_ENABLED === 'true',
                timeout: parseInt(process.env.CLOUDINARY_TIMEOUT) || 7000,
                videoQuality: 'compressed',
                // 使用getter方法避免循环引用
                get accounts() {
                    return config.cloudinary.accounts;
                },
                get pageMapping() {
                    return config.cloudinary.pageMapping;
                }
            },
            tertiary: {
                type: 'vps',
                enabled: process.env.VPS_ENABLED === 'true',
                timeout: parseInt(process.env.VPS_TIMEOUT) || 10000,
                videoQuality: 'local_compressed'
            }
        },

        // H.265压缩参数 (更新配置)
        compression: {
            codec: 'libx265',
            crf: {
                default: 14,
                'together-days': 16
            },
            preset: 'slow',
            noAudio: true,
            pixelFormat: 'yuv420p'
        },

        // 动态URL生成
        get urls() {
            const r2Domain = process.env.CLOUDFLARE_R2_DOMAIN;

            return {
                r2: r2Domain ? {
                    home: `https://${r2Domain}/love-website/videos/home.mp4`,
                    anniversary: `https://${r2Domain}/love-website/videos/anniversary.mp4`,
                    meetings: `https://${r2Domain}/love-website/videos/meetings.mp4`,
                    memorial: `https://${r2Domain}/love-website/videos/memorial.mp4`,
                    'together-days': `https://${r2Domain}/love-website/videos/together-days.mp4`
                } : null,
                vps: {
                    home: `/video/compressed/home.mp4`,
                    anniversary: `/video/compressed/anniversary.mp4`,
                    meetings: `/video/compressed/meetings.mp4`,
                    memorial: `/video/compressed/memorial.mp4`,
                    'together-days': `/video/compressed/together-days.mp4`
                }
            };
        },

        // 宏配置策略 (完整测试支持)
        get macroConfig() {
            const strategy = process.env.VIDEO_LOADING_STRATEGY || 'DEFAULT_LOADING_ORDER';

            const strategies = {
                // 生产环境策略
                DEFAULT_LOADING_ORDER: ['primary', 'secondary', 'tertiary'],    // R2 → Cloudinary → VPS
                CLOUDINARY_FIRST: ['secondary', 'primary', 'tertiary'],         // Cloudinary → R2 → VPS
                VPS_FIRST: ['tertiary', 'primary', 'secondary'],                // VPS → R2 → Cloudinary

                // 单层测试策略 (用于独立验证)
                R2_ONLY: ['primary'],                                           // 仅R2测试
                CLOUDINARY_ONLY: ['secondary'],                                 // 仅Cloudinary测试
                VPS_ONLY: ['tertiary'],                                         // 仅VPS测试

                // 双层测试策略 (用于降级测试)
                R2_CLOUDINARY: ['primary', 'secondary'],                        // R2 → Cloudinary
                R2_VPS: ['primary', 'tertiary'],                               // R2 → VPS
                CLOUDINARY_VPS: ['secondary', 'tertiary'],                      // Cloudinary → VPS

                // 开发环境策略
                DEVELOPMENT: ['tertiary', 'primary', 'secondary'],              // VPS优先(开发)
                TESTING: ['secondary', 'primary', 'tertiary'],                  // Cloudinary优先(测试)

                // 应急策略
                EMERGENCY_VPS: ['tertiary'],                                    // 仅VPS应急
                EMERGENCY_CLOUDINARY: ['secondary']                             // 仅Cloudinary应急
            };

            return {
                strategy: strategy,
                loadingOrder: strategies[strategy] || strategies.DEFAULT_LOADING_ORDER,
                availableStrategies: Object.keys(strategies),
                description: this.getStrategyDescription(strategy)
            };
        },

        // 策略描述
        getStrategyDescription(strategy) {
            const descriptions = {
                DEFAULT_LOADING_ORDER: '生产环境默认策略: R2 → Cloudinary → VPS',
                CLOUDINARY_FIRST: 'Cloudinary优先策略: Cloudinary → R2 → VPS',
                VPS_FIRST: 'VPS优先策略: VPS → R2 → Cloudinary',
                R2_ONLY: '单层测试: 仅使用R2',
                CLOUDINARY_ONLY: '单层测试: 仅使用Cloudinary',
                VPS_ONLY: '单层测试: 仅使用VPS',
                R2_CLOUDINARY: '双层测试: R2 → Cloudinary',
                R2_VPS: '双层测试: R2 → VPS',
                CLOUDINARY_VPS: '双层测试: Cloudinary → VPS',
                DEVELOPMENT: '开发环境: VPS → R2 → Cloudinary',
                TESTING: '测试环境: Cloudinary → R2 → VPS',
                EMERGENCY_VPS: '应急模式: 仅VPS',
                EMERGENCY_CLOUDINARY: '应急模式: 仅Cloudinary'
            };
            return descriptions[strategy] || '未知策略';
        }
    }

};

// 配置验证（仅在非测试环境下执行）
if (process.env.NODE_ENV !== 'test') {
    try {
        validateConfig();
        console.log('✅ Configuration loaded successfully');
        console.log(`   Server: ${config.server.env} mode on port ${config.server.port}`);
        console.log(`   Domain: ${config.domain.url}`);
        console.log(`   Database: ${config.database.path}`);
        console.log(`   Cloudinary: ${config.cloudinary.enabled ? 'enabled' : 'disabled'}`);
        console.log(`   Video Delivery: ${config.videoDelivery.enabled ? 'enabled' : 'disabled'}`);
        if (config.videoDelivery.enabled) {
            console.log(`   Video Strategy: ${config.videoDelivery.macroConfig.strategy}`);
        }
    } catch (error) {
        console.error('❌ Configuration validation failed:', error.message);
        process.exit(1);
    }
}

// 前端配置暴露 (浏览器环境)
if (typeof window !== 'undefined') {
    window.loveConfig = config;
}

// 导出配置对象
module.exports = config;
